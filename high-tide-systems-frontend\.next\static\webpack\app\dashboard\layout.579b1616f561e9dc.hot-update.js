"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js":
/*!*********************************************************************!*\
  !*** ./src/components/admin/preferences/NotificationPreferences.js ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationPreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/ModuleCheckbox */ \"(app-pages-browser)/./src/components/ui/ModuleCheckbox.js\");\n/* harmony import */ var _PreferencesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PreferencesSection */ \"(app-pages-browser)/./src/components/admin/preferences/PreferencesSection.js\");\n/* harmony import */ var _services_preferencesService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/preferencesService */ \"(app-pages-browser)/./src/services/preferencesService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Clock,Database,Download,Shield,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Clock,Database,Download,Shield,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Clock,Database,Download,Shield,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Clock,Database,Download,Shield,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Clock,Database,Download,Shield,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Clock,Database,Download,Shield,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Definir tipos de notificação\nconst NOTIFICATION_TYPES = [\n    {\n        key: \"NEW_REGISTRATION\",\n        label: \"Novos Cadastros\",\n        description: \"Notificações sobre novos cadastros no sistema\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            size: 16\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n            lineNumber: 16,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        key: \"APPOINTMENT_COMING\",\n        label: \"Consultas Chegando\",\n        description: \"Avisos de consultas agendadas (1 hora antes)\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            size: 16\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n            lineNumber: 22,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        key: \"NEW_ACCESS\",\n        label: \"Novos Acessos\",\n        description: \"Notificações sobre novos acessos ao sistema\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            size: 16\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n            lineNumber: 28,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        key: \"NEW_BACKUP\",\n        label: \"Novo Backup\",\n        description: \"Confirmação de backups realizados\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            size: 16\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n            lineNumber: 34,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        key: \"NEW_EXPORT\",\n        label: \"Exportações\",\n        description: \"Notificações sobre exportações concluídas\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            size: 16\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n            lineNumber: 40,\n            columnNumber: 11\n        }, undefined)\n    }\n];\nfunction NotificationPreferences(param) {\n    let { search = \"\", searchMode = false, preferences = null, selectedCompanyId = null, onSave = null } = param;\n    _s();\n    // Estados para preferências de notificação\n    const [enabledNotifications, setEnabledNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [requiredNotifications, setRequiredNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Carregar preferências quando o componente receber os dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPreferences.useEffect\": ()=>{\n            if (preferences && preferences.notifications) {\n                setEnabledNotifications(preferences.notifications.enabled || {});\n                setRequiredNotifications(preferences.notifications.required || {});\n            } else {\n                // Definir valores padrão se não houver preferências\n                const defaultEnabled = {};\n                const defaultRequired = {};\n                NOTIFICATION_TYPES.forEach({\n                    \"NotificationPreferences.useEffect\": (type)=>{\n                        defaultEnabled[type.key] = false;\n                        defaultRequired[type.key] = false;\n                    }\n                }[\"NotificationPreferences.useEffect\"]);\n                setEnabledNotifications(defaultEnabled);\n                setRequiredNotifications(defaultRequired);\n            }\n        }\n    }[\"NotificationPreferences.useEffect\"], [\n        preferences\n    ]);\n    // Filtrar notificações baseado na pesquisa\n    const filteredNotifications = NOTIFICATION_TYPES.filter((notification)=>{\n        if (!search) return true;\n        return notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase());\n    });\n    // Se não há resultados na pesquisa, não mostrar nada\n    if (searchMode && filteredNotifications.length === 0) {\n        return null;\n    }\n    // Função para toggle de notificação habilitada\n    const toggleNotificationEnabled = (key)=>{\n        setEnabledNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n        // Se desabilitar, também desabilitar obrigatoriedade\n        if (enabledNotifications[key]) {\n            setRequiredNotifications((prev)=>({\n                    ...prev,\n                    [key]: false\n                }));\n        }\n    };\n    // Função para toggle de obrigatoriedade\n    const toggleNotificationRequired = (key)=>{\n        setRequiredNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n    };\n    // Função para salvar preferências\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            const notificationPreferences = {\n                enabled: enabledNotifications,\n                required: requiredNotifications\n            };\n            // Manter as preferências existentes e adicionar as de notificação\n            const currentPreferences = preferences || {};\n            const updatedPreferences = {\n                ...currentPreferences,\n                notifications: notificationPreferences\n            };\n            if (onSave) {\n                // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)\n                await onSave(updatedPreferences);\n            } else {\n                // Fallback para o serviço tradicional\n                await _services_preferencesService__WEBPACK_IMPORTED_MODULE_6__.preferencesService.save(updatedPreferences);\n                toast_success(\"Preferências de notificação salvas com sucesso!\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar preferências de notificação:\", error);\n            toast_error(\"Erro ao salvar preferências de notificação\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    if (searchMode && filteredNotifications.length === 0) {\n        return null;\n    }\n    const showNotifications = !searchMode || filteredNotifications.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4\",\n                        children: \"Prefer\\xeancias de Notifica\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-600 dark:text-neutral-300 mb-6\",\n                        children: [\n                            \"Configure quais tipos de notifica\\xe7\\xe3o estar\\xe3o dispon\\xedveis no sistema. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                children: \"habilitadas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 159,\n                                columnNumber: 160\n                            }, this),\n                            \" aparecer\\xe3o para os usu\\xe1rios. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                children: \"obrigat\\xf3rias\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 159,\n                                columnNumber: 286\n                            }, this),\n                            \" n\\xe3o podem ser desativadas pelos usu\\xe1rios.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg text-blue-600 dark:text-blue-400\",\n                                                children: notification.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: notification.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                        children: notification.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                id: \"enabled-\".concat(notification.key),\n                                                checked: enabledNotifications[notification.key] || false,\n                                                onChange: ()=>toggleNotificationEnabled(notification.key),\n                                                disabled: isSaving,\n                                                label: \"Habilitada no sistema\",\n                                                moduleColor: \"admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                id: \"required-\".concat(notification.key),\n                                                checked: requiredNotifications[notification.key] || false,\n                                                onChange: ()=>toggleNotificationRequired(notification.key),\n                                                disabled: !enabledNotifications[notification.key] || isSaving,\n                                                label: \"Obrigat\\xf3ria para todos os usu\\xe1rios\",\n                                                moduleColor: \"admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, notification.key, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"text-blue-600 dark:text-blue-400\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                    children: \"Como funcionam as notifica\\xe7\\xf5es\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Habilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Aparecem nas configura\\xe7\\xf5es do usu\\xe1rio e podem ser ativadas/desativadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Obrigat\\xf3rias:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Ficam sempre ativas e n\\xe3o podem ser desativadas pelo usu\\xe1rio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Desabilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" N\\xe3o aparecem no sistema e n\\xe3o s\\xe3o enviadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• As configura\\xe7\\xf5es se aplicam a todos os usu\\xe1rios da empresa selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                    onClick: handleSave,\n                    disabled: isSaving,\n                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvando...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Clock_Database_Download_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvar Prefer\\xeancias\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationPreferences, \"JlajU5b7Eh4+ce/2mDsMkIE7bNk=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = NotificationPreferences;\nvar _c;\n$RefreshReg$(_c, \"NotificationPreferences\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js\n"));

/***/ })

});